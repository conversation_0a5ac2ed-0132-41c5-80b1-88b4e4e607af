/**
 * 通用js方法封装处理
 * Copyright (c) 2019 ruoyi
 */

// 日期格式化
// 输入 new Date() → 输出 "2025-07-13 16:50:21"
// 输入 time, '{y}/{m}/{d}' → 输出 "2025/07/13"
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  // 如果是Date对象
  if (typeof time === 'object') {
    date = time
  } else {
    // 时间戳
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\.[\d]{3}/gm), '')
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields()
  }
}

// 添加日期范围
/* 
let aaa = {};
let dateRange = ['2025-07-01', '2025-07-31'];

aaa = addDateRange(aaa, dateRange);

console.log(aaa);

结果：其实就是往aaa对象添加了一个 params 对象
{
  params: {
    beginTime: '2025-07-01',
    endTime: '2025-07-31'
  }
} 
*/
export function addDateRange(params, dateRange, propName) {
  let search = params
  // 确保 search.params 是一个对象
  search.params = typeof (search.params) === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {}
  // 确保 dateRange 是数组
  dateRange = Array.isArray(dateRange) ? dateRange : []
  // 没有提供默认键名，就用默认的
  if (typeof (propName) === 'undefined') {
    search.params['beginTime'] = dateRange[0]
    search.params['endTime'] = dateRange[1]
  } else {
    search.params['begin' + propName] = dateRange[0]
    search.params['end' + propName] = dateRange[1]
  }
  return search
}

// 回显数据字典
/*
示例 1：基本用法（找到匹配项）
const datas = {
  0: { label: '男', value: '0' },
  1: { label: '女', value: '1' }
};

let value = '0';
let label = selectDictLabel(datas, value);

console.log(label); // 输出: "男"

示例 2：找不到匹配项时返回原值
const datas = {
  0: { label: '男', value: '0' },
  1: { label: '女', value: '1' }
};

let value = '2';
let label = selectDictLabel(datas, value);

console.log(label); // 输出: "2"
*/

/*
示例 1：基本用法（字符串 + 默认分隔符）
const datas = {
  0: { label: '男', value: '0' },
  1: { label: '女', value: '1' }
};

let value = '0,1';
let labels = selectDictLabels(datas, value);

console.log(labels); // 输出: "男,女"

示例 2：使用自定义分隔符（如 /）
const datas = {
  0: { label: '启用', value: '0' },
  1: { label: '禁用', value: '1' }
};

let value = '0,1';
let labels = selectDictLabels(datas, value, '/');

console.log(labels); // 输出: "启用/禁用"

示例 3：传入数组自动转为字符串
const datas = {
  0: { label: '成功', value: '0' },
  1: { label: '失败', value: '1' }
};

let value = ['0', '1'];
let labels = selectDictLabels(datas, value);

console.log(labels); // 输出: "成功,失败"

示例 4：部分字段未找到匹配项
const datas = {
  0: { label: '成功', value: '0' },
  1: { label: '失败', value: '1' }
};

let value = '0,2,1';
let labels = selectDictLabels(datas, value);

console.log(labels); // 输出: "成功,2,失败"
*/
export function selectDictLabel(datas, value) {
  if (value === undefined) {
    return ""
  }
  var actions = []
  Object.keys(datas).some((key) => {
    if (datas[key].value == ('' + value)) {
      actions.push(datas[key].label)
      return true
    }
  })
  if (actions.length === 0) {
    actions.push(value)
  }
  return actions.join('')
}

// 回显数据字典（字符串、数组）
export function selectDictLabels(datas, value, separator) {
  if (value === undefined || value.length ===0) {
    return ""
  }
  if (Array.isArray(value)) {
    value = value.join(",")
  }
  var actions = []
  var currentSeparator = undefined === separator ? "," : separator
  var temp = value.split(currentSeparator)
  Object.keys(value.split(currentSeparator)).some((val) => {
    var match = false
    Object.keys(datas).some((key) => {
      if (datas[key].value == ('' + temp[val])) {
        actions.push(datas[key].label + currentSeparator)
        match = true
      }
    })
    if (!match) {
      actions.push(temp[val] + currentSeparator)
    }
  })
  return actions.join('').substring(0, actions.join('').length - 1)
}

// 字符串格式化(%s )
/*
示例 1：基本用法
let result = sprintf("你好，%s！", "世界");
console.log(result); // 输出: 你好，世界！
示例 2：多个占位符替换
let result = sprintf("用户：%s，ID：%s", "张三", "1001");
console.log(result); // 输出: 用户：张三，ID：1001
示例 3：遇到 undefined 不替换
let result = sprintf("姓名：%s，年龄：%s", "李四");
console.log(result); // 输出: 姓名：李四，年龄：
*/
export function sprintf(str) {
  var args = arguments, flag = true, i = 1
  str = str.replace(/%s/g, function () {
    var arg = args[i++]
    if (typeof arg === 'undefined') {
      flag = false
      return ''
    }
    return arg
  })
  return flag ? str : ''
}

// 转换字符串，undefined,null等转化为""
export function parseStrEmpty(str) {
  if (!str || str == "undefined" || str == "null") {
    return ""
  }
  return str
}

// 递归合并两个对象，如果目标对象（target）中的某个属性值是对象，则递归合并；否则直接覆盖源对象（source）的对应属性。
/*
示例 1：基本用法（简单对象合并）
let source = { a: 1, b: { x: 10 } };
let target = { b: { y: 20 }, c: 3 };

let result = mergeRecursive(source, target);

console.log(result);
// 输出:
// {
//   a: 1,
//   b: { x: 10, y: 20 },
//   c: 3
// }

示例 2：深层嵌套对象合并
let source = {
  config: {
    theme: { color: 'blue', size: 'small' },
    layout: 'vertical'
  }
};

let target = {
  config: {
    theme: { color: 'red' },
    mode: 'dark'
  }
};

let result = mergeRecursive(source, target);

console.log(result);
// 输出:
// {
//   config: {
//     theme: { color: 'red', size: 'small' },
//     layout: 'vertical',
//     mode: 'dark'
//   }
// }
*/
export function mergeRecursive(source, target) {
  for (var p in target) {
    try {
      if (target[p].constructor == Object) {
        source[p] = mergeRecursive(source[p], target[p])
      } else {
        source[p] = target[p]
      }
    } catch (e) {
      source[p] = target[p]
    }
  }
  return source
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
  let config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children'
  }

  var childrenListMap = {}
  var tree = []
  for (let d of data) {
    let id = d[config.id]
    childrenListMap[id] = d
    if (!d[config.childrenList]) {
      d[config.childrenList] = []
    }
  }

  for (let d of data) {
    let parentId = d[config.parentId]
    let parentObj = childrenListMap[parentId]
    if (!parentObj) {
      tree.push(d)
    } else {
      parentObj[config.childrenList].push(d)
    }
  }
  return tree
}

/**
* 参数处理（将键值对转换为url查询字符串）
* @param {*} params  参数
*/
export function tansParams(params) {
  let result = ''
  for (const propName of Object.keys(params)) {
    const value = params[propName]
    var part = encodeURIComponent(propName) + "="
    if (value !== null && value !== "" && typeof (value) !== "undefined") {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && value[key] !== "" && typeof (value[key]) !== 'undefined') {
            let params = propName + '[' + key + ']'
            var subPart = encodeURIComponent(params) + "="
            result += subPart + encodeURIComponent(value[key]) + "&"
          }
        }
      } else {
        result += part + encodeURIComponent(value) + "&"
      }
    }
  }
  return result
}

// 返回项目路径
export function getNormalPath(p) {
  if (p.length === 0 || !p || p == 'undefined') {
    return p
  }
  let res = p.replace('//', '/')
  if (res[res.length - 1] === '/') {
    // 截取(左闭右开)，表示不要最后一个字符
    return res.slice(0, res.length - 1)
  }
  return res
}

// 验证是否为blob格式
export function blobValidate(data) {
  return data.type !== 'application/json'
}
