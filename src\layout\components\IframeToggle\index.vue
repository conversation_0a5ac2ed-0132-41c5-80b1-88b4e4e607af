<!--
  Iframe 切换管理组件 (Iframe Toggle Component)

  功能说明：
  1. 管理多个 iframe 页面的显示切换
  2. 根据当前路由动态显示对应的 iframe
  3. 支持 URL 参数传递和拼接
  4. 与标签页视图 (TagsView) 联动工作
  5. 实现内嵌页面的无缝切换体验

  使用场景：
  - 内嵌第三方系统页面
  - 多标签页 iframe 管理
  - 外部链接页面集成
  - 微前端架构支持
-->
<template>
  <inner-link
    v-for="(item, index) in tagsViewStore.iframeViews"
    :key="item.path"
    :iframeId="'iframe' + index"
    v-show="route.path === item.path"
    :src="iframeUrl(item.meta.link, item.query)"
  ></inner-link>
</template>

<script setup>
import InnerLink from "../InnerLink/index"
import useTagsViewStore from "@/store/modules/tagsView"

const route = useRoute()
const tagsViewStore = useTagsViewStore()

function iframeUrl(url, query) {
  if (Object.keys(query).length > 0) {
    let params = Object.keys(query).map((key) => key + "=" + query[key]).join("&")
    return url + "?" + params
  }
  return url
}
</script>
