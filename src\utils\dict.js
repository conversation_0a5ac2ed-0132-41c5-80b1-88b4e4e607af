import useDictStore from '@/store/modules/dict'
import { getDicts } from '@/api/system/dict/data'

/**
 * 获取字典数据
 * 接收多个字典类型参数（通过 ...args）。
 * 尝试从本地缓存 (useDictStore) 获取字典数据，如果存在则直接使用。
 * 若缓存中不存在，则调用 getDicts 方法从后端获取数据，并将其转换为 { label, value, elTagType, elTagClass } 格式。
 * 将结果保存到响应式对象 res 中，并通过 toRefs 返回响应式引用，以便在 Vue 组件中使用。
 * 
 * toRefs可以将响应式对象的属性转化为ref，以便在解构时保留响应式
 * 
 * 举例
 * 输入 useDict('user_type', 'status');
 * 输出：
 * {
  userType: [
    { label: '管理员', value: 'admin', elTagType: 'primary', elTagClass: '' },
    { label: '普通用户', value: 'user', elTagType: 'success', elTagClass: '' }
  ],
  status: [
    { label: '启用', value: '1', elTagType: 'success', elTagClass: '' },
    { label: '禁用', value: '0', elTagType: 'danger', elTagClass: '' }
  ]
}
 */
export function useDict(...args) {
  const res = ref({})
  return (() => {
    args.forEach((dictType, index) => {
      res.value[dictType] = []
      const dicts = useDictStore().getDict(dictType)
      if (dicts) {
        res.value[dictType] = dicts
      } else {
        getDicts(dictType).then(resp => {
          res.value[dictType] = resp.data.map(p => ({ label: p.dictLabel, value: p.dictValue, elTagType: p.listClass, elTagClass: p.cssClass }))
          useDictStore().setDict(dictType, res.value[dictType])
        })
      }
    })
    return toRefs(res.value)
  })()
}