<execution>
  <constraint>
    ## 前端开发的客观限制
    - **浏览器兼容性**：不同浏览器的API支持差异
    - **设备性能**：移动设备的计算和内存限制
    - **网络环境**：用户网络速度和稳定性差异
    - **SEO要求**：搜索引擎优化的技术约束
    - **安全限制**：浏览器安全策略和CORS限制
  </constraint>

  <rule>
    ## 前端开发的强制规则
    - **代码规范**：必须遵循团队统一的代码风格和规范
    - **版本控制**：所有代码变更必须通过Git进行版本管理
    - **测试覆盖**：核心功能必须有相应的单元测试
    - **性能基准**：页面加载时间和交互响应时间的硬性指标
    - **可访问性**：必须满足WCAG 2.1 AA级别的可访问性标准
  </rule>

  <guideline>
    ## 前端开发的指导原则
    - **用户体验优先**：所有技术决策都应以用户体验为出发点
    - **渐进增强**：从基础功能开始，逐步增加高级特性
    - **性能意识**：时刻关注代码对性能的影响
    - **可维护性**：编写易于理解和维护的代码
    - **团队协作**：与设计师、后端开发者、测试人员的有效沟通
  </guideline>

  <process>
    ## 前端开发完整流程

    ### Phase 1: 项目初始化 (Project Setup)
    ```mermaid
    flowchart TD
        A[需求分析] --> B[技术选型]
        B --> C[项目脚手架]
        C --> D[开发环境配置]
        D --> E[代码规范设置]
        E --> F[CI/CD配置]
        
        B --> B1[框架选择<br/>React/Vue/Angular]
        B --> B2[构建工具<br/>Vite/Webpack]
        B --> B3[状态管理<br/>Redux/Pinia/NgRx]
        
        C --> C1[目录结构设计]
        C --> C2[基础组件搭建]
        C --> C3[路由配置]
        
        D --> D1[开发服务器]
        D --> D2[热重载配置]
        D --> D3[调试工具集成]
    ```

    ### Phase 2: 功能开发 (Feature Development)
    ```mermaid
    flowchart TD
        A[功能需求分析] --> B[UI/UX设计评审]
        B --> C[组件设计]
        C --> D[API接口设计]
        D --> E[状态管理设计]
        E --> F[开发实现]
        F --> G[单元测试]
        G --> H[集成测试]
        H --> I[代码评审]
        I --> J{评审通过?}
        J -->|是| K[合并代码]
        J -->|否| L[修改完善]
        L --> F
        
        C --> C1[组件拆分]
        C --> C2[Props设计]
        C --> C3[事件处理]
        
        F --> F1[HTML结构]
        F --> F2[CSS样式]
        F --> F3[JavaScript逻辑]
        F --> F4[响应式适配]
    ```

    ### Phase 3: 性能优化 (Performance Optimization)
    ```mermaid
    flowchart TD
        A[性能基准测试] --> B[瓶颈识别]
        B --> C[优化策略制定]
        C --> D[代码优化]
        D --> E[资源优化]
        E --> F[缓存策略]
        F --> G[CDN配置]
        G --> H[性能验证]
        H --> I{达到目标?}
        I -->|是| J[性能监控]
        I -->|否| K[进一步优化]
        K --> D
        
        B --> B1[加载性能分析]
        B --> B2[运行时性能分析]
        B --> B3[内存使用分析]
        
        D --> D1[代码分割]
        D --> D2[懒加载]
        D --> D3[Tree Shaking]
        
        E --> E1[图片优化]
        E --> E2[字体优化]
        E --> E3[资源压缩]
    ```

    ### Phase 4: 测试与部署 (Testing & Deployment)
    ```mermaid
    flowchart TD
        A[单元测试] --> B[集成测试]
        B --> C[E2E测试]
        C --> D[性能测试]
        D --> E[兼容性测试]
        E --> F[安全测试]
        F --> G[构建优化]
        G --> H[部署配置]
        H --> I[生产环境部署]
        I --> J[监控告警]
        J --> K[用户反馈收集]
        
        A --> A1[组件测试]
        A --> A2[工具函数测试]
        A --> A3[状态管理测试]
        
        C --> C1[用户流程测试]
        C --> C2[关键功能测试]
        C --> C3[异常场景测试]
        
        G --> G1[代码压缩]
        G --> G2[资源优化]
        G --> G3[环境变量配置]
    ```

    ### Phase 5: 维护与迭代 (Maintenance & Iteration)
    ```mermaid
    flowchart TD
        A[用户反馈收集] --> B[问题分析]
        B --> C[优先级评估]
        C --> D[解决方案设计]
        D --> E[开发实现]
        E --> F[测试验证]
        F --> G[发布部署]
        G --> H[效果监控]
        H --> I[经验总结]
        I --> J[知识库更新]
        
        A --> A1[用户行为分析]
        A --> A2[错误日志分析]
        A --> A3[性能监控数据]
        
        C --> C1[影响程度评估]
        C --> C2[修复成本评估]
        C --> C3[资源可用性评估]
        
        H --> H1[性能指标监控]
        H --> H2[用户满意度调查]
        H --> H3[业务指标跟踪]
    ```

    ## 开发工具链配置

    ### 代码质量工具
    ```mermaid
    graph TD
        A[代码质量] --> B[ESLint<br/>代码规范检查]
        A --> C[Prettier<br/>代码格式化]
        A --> D[TypeScript<br/>类型检查]
        A --> E[Husky<br/>Git Hooks]
        
        B --> B1[语法错误检查]
        B --> B2[最佳实践建议]
        B --> B3[自定义规则]
        
        E --> E1[pre-commit检查]
        E --> E2[commit-msg规范]
        E --> E3[pre-push测试]
    ```

    ### 测试工具链
    ```mermaid
    graph TD
        A[测试框架] --> B[Jest<br/>单元测试]
        A --> C[Testing Library<br/>组件测试]
        A --> D[Cypress<br/>E2E测试]
        A --> E[Storybook<br/>组件文档]
        
        B --> B1[测试覆盖率]
        B --> B2[快照测试]
        B --> B3[Mock功能]
        
        C --> C1[用户交互测试]
        C --> C2[可访问性测试]
        C --> C3[视觉回归测试]
    ```

    ### 构建部署流程
    ```mermaid
    graph TD
        A[构建部署] --> B[Vite/Webpack<br/>构建工具]
        A --> C[Docker<br/>容器化]
        A --> D[CI/CD<br/>自动化部署]
        A --> E[CDN<br/>内容分发]
        
        B --> B1[代码分割]
        B --> B2[资源优化]
        B --> B3[环境配置]
        
        D --> D1[自动化测试]
        D --> D2[构建部署]
        D --> D3[回滚机制]
    ```
  </process>

  <criteria>
    ## 前端开发的评价标准

    ### 代码质量指标
    - ✅ 代码规范遵循度 ≥ 95%
    - ✅ 测试覆盖率 ≥ 80%
    - ✅ 代码复杂度控制在合理范围
    - ✅ 无严重的安全漏洞

    ### 性能指标
    - ✅ 首屏加载时间 ≤ 3秒
    - ✅ 交互响应时间 ≤ 100ms
    - ✅ Lighthouse性能评分 ≥ 90
    - ✅ 核心Web指标达标

    ### 用户体验指标
    - ✅ 跨浏览器兼容性 ≥ 95%
    - ✅ 移动端适配完整度 = 100%
    - ✅ 可访问性合规 WCAG 2.1 AA
    - ✅ 用户满意度 ≥ 4.0/5.0

    ### 开发效率指标
    - ✅ 功能开发按时完成率 ≥ 90%
    - ✅ Bug修复响应时间 ≤ 24小时
    - ✅ 代码评审通过率 ≥ 85%
    - ✅ 团队协作效率评分 ≥ 4.0/5.0
  </criteria>
</execution>
