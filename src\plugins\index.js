import tab from './tab'
import auth from './auth'
import cache from './cache'
import modal from './modal'
import download from './download'

// Vue 插件注册模块，用于将多个工具对象挂载为 Vue 应用的全局属性
// 所有被挂载的对象都可以在组件中通过 this.$xxx 调用，例如：this.$download.name('test.txt')。
export default function installPlugins(app){
  // 页签操作
  app.config.globalProperties.$tab = tab
  // 认证对象
  app.config.globalProperties.$auth = auth
  // 缓存对象
  app.config.globalProperties.$cache = cache
  // 模态框对象
  app.config.globalProperties.$modal = modal
  // 下载文件
  app.config.globalProperties.$download = download
}
