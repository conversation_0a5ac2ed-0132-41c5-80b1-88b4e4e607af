// 这个文件是一个封装良好的页面滚动动画工具模块，适用于 Vue 或其他前端项目中，提供流畅的滚动体验

/**
 * 二次缓入缓出动画函数
 * Quadratic ease-in-out animation function
 * @param {number} t 当前时间
 * @param {number} b 起始值
 * @param {number} c 变化量
 * @param {number} d 持续时间
 * @returns {number} 计算后的值
 * @example
 * // 在500ms内从0滚动到100的中间某个时刻的值
 * Math.easeInOutQuad(250, 0, 100, 500) // 约50（中点值）
 * Math.easeInOutQuad(125, 0, 100, 500) // 约25（1/4点值）
 * Math.easeInOutQuad(375, 0, 100, 500) // 约75（3/4点值）
 */
Math.easeInOutQuad = function(t, b, c, d) {
  t /= d / 2
  if (t < 1) {
    return c / 2 * t * t + b
  }
  t--
  return -c / 2 * (t * (t - 2) - 1) + b
}

/**
 * 跨浏览器的requestAnimationFrame实现
 * requestAnimationFrame for Smart Animating http://goo.gl/sx5sts
 * @example
 * requestAnimFrame(() => {
 *   console.log('动画帧执行')
 * })
 */
var requestAnimFrame = (function() {
  return window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || function(callback) { window.setTimeout(callback, 1000 / 60) }
})()

/**
 * 移动滚动位置到指定数值
 * Because it's so fucking difficult to detect the scrolling element, just move them all
 * @param {number} amount 滚动位置
 * @example
 * move(0) // 滚动到页面顶部
 * move(500) // 滚动到距离顶部500px的位置
 * move(document.body.scrollHeight) // 滚动到页面底部
 */
function move(amount) {
  document.documentElement.scrollTop = amount
  document.body.parentNode.scrollTop = amount
  document.body.scrollTop = amount
}

/**
 * 获取当前滚动位置
 * @returns {number} 当前滚动位置
 * @example
 * const currentPos = position() // 获取当前滚动位置
 * console.log(`当前滚动位置: ${currentPos}px`)
 *
 * // 检查是否滚动到顶部
 * if (position() === 0) {
 *   console.log('已在页面顶部')
 * }
 */
function position() {
  return document.documentElement.scrollTop || document.body.parentNode.scrollTop || document.body.scrollTop
}

/**
 * 平滑滚动到指定位置
 * @param {number} to 目标滚动位置
 * @param {number} duration 动画持续时间（毫秒）
 * @param {Function} callback 动画完成后的回调函数
 * @example
 * // 基本用法：滚动到页面顶部
 * scrollTo(0)
 *
 * // 滚动到指定位置，自定义动画时间
 * scrollTo(500, 1000) // 1秒内滚动到500px位置
 *
 * // 滚动到页面底部
 * scrollTo(document.body.scrollHeight, 800)
 *
 * // 带回调函数的滚动
 * scrollTo(300, 600, () => {
 *   console.log('滚动完成！')
 *   // 可以在这里执行滚动完成后的操作
 * })
 *
 * // 滚动到特定元素位置
 * const element = document.getElementById('target')
 * const elementTop = element.offsetTop
 * scrollTo(elementTop, 500, () => {
 *   element.focus() // 滚动完成后聚焦元素
 * })
 *
 * // 快速滚动（200ms）
 * scrollTo(0, 200)
 *
 * // 慢速滚动（2秒）
 * scrollTo(1000, 2000)
 */
export function scrollTo(to, duration, callback) {
  const start = position()
  const change = to - start
  const increment = 20
  let currentTime = 0
  duration = (typeof (duration) === 'undefined') ? 500 : duration
  var animateScroll = function() {
    // increment the time
    currentTime += increment
    // find the value with the quadratic in-out easing function
    var val = Math.easeInOutQuad(currentTime, start, change, duration)
    // move the document.body
    move(val)
    // do the animation unless its over
    if (currentTime < duration) {
      requestAnimFrame(animateScroll)
    } else {
      if (callback && typeof (callback) === 'function') {
        // the animation is done so lets callback
        callback()
      }
    }
  }
  animateScroll()
}
