import Cookies from 'js-cookie'

const useAppStore = defineStore(
  'app',
  {
    state: () => ({
      sidebar: {
        // + 表示转换字符串为数字，!!表示转换为布尔值
        opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
        withoutAnimation: false,  // 默认不跳过动画效果
        hide: false
      },
      device: 'desktop',
      size: Cookies.get('size') || 'default' // UI组件大小
    }),
    actions: {
      // 切换侧边栏的展开/收起状态，并根据状态设置 Cookie 中的 sidebarStatus（1 表示展开，0 表示收起）。若当前为隐藏状态则不操作。
      toggleSideBar(withoutAnimation) {
        if (this.sidebar.hide) {
          return false
        }
        this.sidebar.opened = !this.sidebar.opened
        this.sidebar.withoutAnimation = withoutAnimation
        if (this.sidebar.opened) {
          Cookies.set('sidebarStatus', 1)
        } else {
          Cookies.set('sidebarStatus', 0)
        }
      },
      // 关闭侧边栏并设置 Cookie 中的 sidebarStatus 为 0，同时控制动画是否启用。
      closeSideBar({ withoutAnimation }) {
        Cookies.set('sidebarStatus', 0)
        this.sidebar.opened = false
        this.sidebar.withoutAnimation = withoutAnimation
      },
      // 切换设备类型
      toggleDevice(device) {
        this.device = device
      },
      // 设置 UI 组件大小
      setSize(size) {
        this.size = size
        Cookies.set('size', size)
      },
      // 切换侧边栏的隐藏/显示状态
      toggleSideBarHide(status) {
        this.sidebar.hide = status
      }
    }
  })

export default useAppStore
