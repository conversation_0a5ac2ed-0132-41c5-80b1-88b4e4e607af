<!--
  内嵌链接组件 (Inner Link Component)

  功能说明：
  1. 提供 iframe 容器用于嵌入外部页面
  2. 自动计算并设置合适的容器高度
  3. 显示页面加载状态和进度提示
  4. 监听 iframe 加载完成事件
  5. 支持自定义 iframe ID 标识

  使用场景：
  - 嵌入第三方系统页面
  - 显示外部文档或报表
  - 集成其他 Web 应用
  - 实现页面内容隔离
-->
<template>
  <div :style="'height:' + height" v-loading="loading" element-loading-text="正在加载页面，请稍候！">
    <iframe
      :id="iframeId"
      style="width: 100%; height: 100%"
      :src="src"
      ref="iframeRef"
      frameborder="no"
    ></iframe>
  </div>
</template>

<script setup>
const props = defineProps({
  src: {
    type: String,
    default: "/"
  },
  iframeId: {
    type: String
  }
})

const loading = ref(true)
const height = ref(document.documentElement.clientHeight - 94.5 + 'px')
const iframeRef = ref(null)

onMounted(() => {
  if (iframeRef.value) {
    iframeRef.value.onload = () => {
      loading.value = false
    }
  }
})
</script>
