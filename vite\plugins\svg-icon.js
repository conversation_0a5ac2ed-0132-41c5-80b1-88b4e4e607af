// 创建一个 SVG 图标插件，以便在 Vue3 项目中使用 Vite 构建工具支持 SVG 图标组件化管理
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import path from 'path'

export default function createSvgIcon(isBuild) {
    return createSvgIconsPlugin({
        // 指定存放 SVG 图标的目录列表, (process.cwd() 获取当前工作目录（通常是项目根目录）,然后做了路径拼接)
		iconDirs: [path.resolve(process.cwd(), 'src/assets/icons/svg')],
        // 设置图标命名规则
        // symbolId：定义 SVG 图标转换为 <symbol> 标签时的 id 命名格式。
        // 示例：
        //     若图标路径为 src/assets/icons/svg/user/add.svg
        //     则生成的 id 为 icon-user-add
        symbolId: 'icon-[dir]-[name]',
        // 是否启用 SVGO 对 SVG 文件进行优化压缩（开发环境不压缩）
        svgoOptions: isBuild
    })
}
