import { parseTime } from './ruoyi'

/**
 * 表格时间格式化
 * @example
 * formatDate('2023-12-25T10:30:45.123Z') // '2023-12-25 18:30:45'
 * formatDate(1703505045123) // '2023-12-25 18:30:45'
 * formatDate(null) // ''
 */
export function formatDate(cellValue) {
  if (cellValue == null || cellValue == "") return ""
  var date = new Date(cellValue)
  var year = date.getFullYear()
  var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
}

/**
 * 格式化时间为相对时间或指定格式
 * @param {number} time 时间戳（秒或毫秒）
 * @param {string} option 时间格式选项
 * @returns {string} 格式化后的时间字符串
 * @example
 * formatTime(Date.now()) // '刚刚'
 * formatTime(Date.now() - 60000) // '1分钟前'
 * formatTime(Date.now() - 3600000) // '1小时前'
 * formatTime(Date.now() - 86400000) // '1天前'
 * formatTime(1703505045, '{y}-{m}-{d}') // '2023-12-25'
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * 解析URL查询参数为对象
 * @param {string} url URL字符串
 * @returns {Object} 查询参数对象
 * @example
 * getQueryObject('https://example.com?name=张三&age=25&city=北京')
 * // { name: '张三', age: '25', city: '北京' }
 * getQueryObject() // 解析当前页面URL的查询参数
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 * 计算UTF-8字符串的字节长度
 * @param {string} input value 输入字符串
 * @returns {number} output value 字节长度
 * @example
 * byteLength('hello') // 5
 * byteLength('你好') // 6
 * byteLength('hello世界') // 11
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i)
    if (code > 0x7f && code <= 0x7ff) s++
    else if (code > 0x7ff && code <= 0xffff) s += 2
    if (code >= 0xDC00 && code <= 0xDFFF) i--
  }
  return s
}

/**
 * 清理数组中的假值元素
 * @param {Array} actual 原始数组
 * @returns {Array} 清理后的数组
 * @example
 * cleanArray([0, 1, false, 2, '', 3, null, undefined, 4])
 * // [1, 2, 3, 4]
 * cleanArray(['', 'hello', 0, 'world', false])
 * // ['hello', 'world']
 */
export function cleanArray(actual) {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}

/**
 * 将对象转换为URL查询参数字符串
 * @param {Object} json 参数对象
 * @returns {Array} URL编码的查询字符串
 * @example
 * param({ name: '张三', age: 25, city: '北京' })
 * // 'name=%E5%BC%A0%E4%B8%89&age=25&city=%E5%8C%97%E4%BA%AC'
 * param({ a: 1, b: undefined, c: 3 })
 * // 'a=1&c=3'
 */
export function param(json) {
  if (!json) return ''
  return cleanArray(
    Object.keys(json).map(key => {
      if (json[key] === undefined) return ''
      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
    })
  ).join('&')
}

/**
 * 将URL查询参数字符串转换为对象
 * @param {string} url 包含查询参数的URL
 * @returns {Object} 解析后的参数对象
 * @example
 * param2Obj('https://example.com?name=张三&age=25')
 * // { name: '张三', age: '25' }
 * param2Obj('?a=1&b=2&c=hello%20world')
 * // { a: '1', b: '2', c: 'hello world' }
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach(v => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
 * 将HTML字符串转换为纯文本
 * @param {string} val HTML字符串
 * @returns {string} 纯文本内容
 * @example
 * html2Text('<p>Hello <strong>World</strong>!</p>')
 * // 'Hello World!'
 * html2Text('<div><span>测试</span><br/>内容</div>')
 * // '测试内容'
 */
export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}

/**
 * 合并两个对象，后者优先级更高
 * Merges two objects, giving the last one precedence
 * @param {Object} target 目标对象
 * @param {(Object|Array)} source 源对象或数组
 * @returns {Object} 合并后的对象
 * @example
 * objectMerge({ a: 1, b: 2 }, { b: 3, c: 4 })
 * // { a: 1, b: 3, c: 4 }
 * objectMerge({ user: { name: 'John' } }, { user: { age: 25 } })
 * // { user: { name: 'John', age: 25 } }
 */
export function objectMerge(target, source) {
  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach(property => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === 'object') {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}

/**
 * 切换元素的CSS类名
 * @param {HTMLElement} element DOM元素
 * @param {string} className 类名
 * @example
 * const div = document.querySelector('.my-div')
 * toggleClass(div, 'active') // 如果有active类则移除，没有则添加
 * toggleClass(div, 'hidden') // 切换hidden类
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += '' + className
  } else {
    classString =
      classString.substring(0, nameIndex) +
      classString.substring(nameIndex + className.length)
  }
  element.className = classString
}

/**
 * 获取时间戳
 * @param {string} type 时间类型：'start'表示90天前，其他表示今天开始
 * @returns {Date} 时间戳
 * @example
 * getTime('start') // 90天前的时间戳
 * getTime('end') // 今天00:00:00的时间戳
 * getTime() // 今天00:00:00的时间戳
 */
export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间（毫秒）
 * @param {boolean} immediate 是否立即执行
 * @return {*} 防抖后的函数
 * @example
 * const debouncedSearch = debounce((query) => {
 *   console.log('搜索:', query)
 * }, 300)
 *
 * // 连续调用只会在最后一次调用300ms后执行
 * debouncedSearch('a')
 * debouncedSearch('ab')
 * debouncedSearch('abc') // 只有这次会执行
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * 深度克隆对象（简单版本）
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source 源对象
 * @returns {Object} 克隆后的对象
 * @example
 * const original = { a: 1, b: { c: 2, d: [3, 4] } }
 * const cloned = deepClone(original)
 * cloned.b.c = 5
 * console.log(original.b.c) // 2 (原对象不受影响)
 * console.log(cloned.b.c) // 5
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

/**
 * 数组去重
 * @param {Array} arr 原始数组
 * @returns {Array} 去重后的数组
 * @example
 * uniqueArr([1, 2, 2, 3, 3, 4]) // [1, 2, 3, 4]
 * uniqueArr(['a', 'b', 'a', 'c']) // ['a', 'b', 'c']
 * uniqueArr([1, '1', 2, '2']) // [1, '1', 2, '2']
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr))
}

/**
 * 创建唯一字符串ID
 * @returns {string} 唯一字符串
 * @example
 * createUniqueString() // 'k8j2l9m3n'
 * createUniqueString() // 'p5q7r1s4t'
 * // 每次调用都会生成不同的字符串
 */
export function createUniqueString() {
  const timestamp = +new Date() + ''
  const randomNum = parseInt((1 + Math.random()) * 65536) + ''
  return (+(randomNum + timestamp)).toString(32)
}

/**
 * 检查元素是否包含指定类名
 * Check if an element has a class
 * @param {HTMLElement} elm DOM元素
 * @param {string} cls 类名
 * @returns {boolean} 是否包含类名
 * @example
 * const div = document.querySelector('.my-div')
 * hasClass(div, 'active') // true 或 false
 * hasClass(div, 'hidden') // true 或 false
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}

/**
 * 为元素添加类名
 * Add class to element
 * @param {HTMLElement} elm DOM元素
 * @param {string} cls 要添加的类名
 * @example
 * const div = document.querySelector('.my-div')
 * addClass(div, 'active') // 添加active类
 * addClass(div, 'highlight') // 添加highlight类
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls
}

/**
 * 从元素移除类名
 * Remove class from element
 * @param {HTMLElement} elm DOM元素
 * @param {string} cls 要移除的类名
 * @example
 * const div = document.querySelector('.my-div')
 * removeClass(div, 'active') // 移除active类
 * removeClass(div, 'hidden') // 移除hidden类
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}

/**
 * 创建映射表函数
 * @param {string} str 逗号分隔的字符串
 * @param {boolean} expectsLowerCase 是否转换为小写
 * @returns {Function} 检查函数
 * @example
 * const isHTMLTag = makeMap('div,span,p,a,img', true)
 * isHTMLTag('DIV') // true
 * isHTMLTag('span') // true
 * isHTMLTag('custom') // undefined
 *
 * const isValidType = makeMap('string,number,boolean')
 * isValidType('string') // true
 * isValidType('object') // undefined
 */
export function makeMap(str, expectsLowerCase) {
  const map = Object.create(null)
  const list = str.split(',')
  for (let i = 0; i < list.length; i++) {
    map[list[i]] = true
  }
  return expectsLowerCase
    ? val => map[val.toLowerCase()]
    : val => map[val]
}
/**
 * 导出默认值字符串常量
 * @example
 * const code = exportDefault + '{ name: "test" }'
 * // 'export default { name: "test" }'
 */
export const exportDefault = 'export default '

/**
 * 代码美化配置
 * @example
 * import beautifier from 'js-beautify'
 * const beautifiedHtml = beautifier.html(htmlCode, beautifierConf.html)
 * const beautifiedJs = beautifier.js(jsCode, beautifierConf.js)
 */
export const beautifierConf = {
  html: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'separate',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: false,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  },
  js: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'normal',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: true,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  }
}

/**
 * 首字母大写转换
 * @param {string} str 输入字符串
 * @returns {string} 首字母大写的字符串
 * @example
 * titleCase('hello world') // 'Hello World'
 * titleCase('user name') // 'User Name'
 * titleCase('API response') // 'API Response'
 */
export function titleCase(str) {
  return str.replace(/( |^)[a-z]/g, L => L.toUpperCase())
}

/**
 * 下划线转驼峰命名
 * @param {string} str 下划线格式的字符串
 * @returns {string} 驼峰格式的字符串
 * @example
 * camelCase('user_name') // 'userName'
 * camelCase('api_response_data') // 'apiResponseData'
 * camelCase('create_time') // 'createTime'
 */
export function camelCase(str) {
  return str.replace(/_[a-z]/g, str1 => str1.substring(1).toUpperCase())
}

/**
 * 检查字符串是否为数字格式
 * @param {string} str 要检查的字符串
 * @returns {boolean} 是否为数字格式
 * @example
 * isNumberStr('123') // true
 * isNumberStr('123.45') // true
 * isNumberStr('+123') // true
 * isNumberStr('-123.45') // true
 * isNumberStr('abc') // false
 * isNumberStr('12.34.56') // false
 */
export function isNumberStr(str) {
  return /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(str)
}
 
