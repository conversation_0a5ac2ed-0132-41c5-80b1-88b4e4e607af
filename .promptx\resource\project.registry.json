{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-21T02:02:29.099Z", "updatedAt": "2025-07-21T02:02:29.107Z", "resourceCount": 18}, "resources": [{"id": "design-process", "source": "project", "protocol": "execution", "name": "Design Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ui-ux-designer/execution/design-process.execution.md", "metadata": {"createdAt": "2025-07-21T02:02:29.100Z", "updatedAt": "2025-07-21T02:02:29.100Z", "scannedAt": "2025-07-21T02:02:29.100Z", "path": "domain/ui-ux-designer/execution/design-process.execution.md"}}, {"id": "design-validation", "source": "project", "protocol": "execution", "name": "Design Validation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ui-ux-designer/execution/design-validation.execution.md", "metadata": {"createdAt": "2025-07-21T02:02:29.101Z", "updatedAt": "2025-07-21T02:02:29.101Z", "scannedAt": "2025-07-21T02:02:29.101Z", "path": "domain/ui-ux-designer/execution/design-validation.execution.md"}}, {"id": "user-centered-design", "source": "project", "protocol": "execution", "name": "User Centered Design 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ui-ux-designer/execution/user-centered-design.execution.md", "metadata": {"createdAt": "2025-07-21T02:02:29.101Z", "updatedAt": "2025-07-21T02:02:29.101Z", "scannedAt": "2025-07-21T02:02:29.101Z", "path": "domain/ui-ux-designer/execution/user-centered-design.execution.md"}}, {"id": "design-tools", "source": "project", "protocol": "knowledge", "name": "Design Tools 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ui-ux-designer/knowledge/design-tools.knowledge.md", "metadata": {"createdAt": "2025-07-21T02:02:29.101Z", "updatedAt": "2025-07-21T02:02:29.101Z", "scannedAt": "2025-07-21T02:02:29.101Z", "path": "domain/ui-ux-designer/knowledge/design-tools.knowledge.md"}}, {"id": "frontend-collaboration", "source": "project", "protocol": "knowledge", "name": "Frontend Collaboration 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ui-ux-designer/knowledge/frontend-collaboration.knowledge.md", "metadata": {"createdAt": "2025-07-21T02:02:29.102Z", "updatedAt": "2025-07-21T02:02:29.102Z", "scannedAt": "2025-07-21T02:02:29.102Z", "path": "domain/ui-ux-designer/knowledge/frontend-collaboration.knowledge.md"}}, {"id": "ui-ux-expertise", "source": "project", "protocol": "knowledge", "name": "Ui Ux Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ui-ux-designer/knowledge/ui-ux-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-21T02:02:29.102Z", "updatedAt": "2025-07-21T02:02:29.102Z", "scannedAt": "2025-07-21T02:02:29.102Z", "path": "domain/ui-ux-designer/knowledge/ui-ux-expertise.knowledge.md"}}, {"id": "aesthetic-judgment", "source": "project", "protocol": "thought", "name": "Aesthetic Judgment 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ui-ux-designer/thought/aesthetic-judgment.thought.md", "metadata": {"createdAt": "2025-07-21T02:02:29.102Z", "updatedAt": "2025-07-21T02:02:29.102Z", "scannedAt": "2025-07-21T02:02:29.102Z", "path": "domain/ui-ux-designer/thought/aesthetic-judgment.thought.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ui-ux-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-07-21T02:02:29.103Z", "updatedAt": "2025-07-21T02:02:29.103Z", "scannedAt": "2025-07-21T02:02:29.103Z", "path": "domain/ui-ux-designer/thought/design-thinking.thought.md"}}, {"id": "user-empathy", "source": "project", "protocol": "thought", "name": "User Empathy 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ui-ux-designer/thought/user-empathy.thought.md", "metadata": {"createdAt": "2025-07-21T02:02:29.103Z", "updatedAt": "2025-07-21T02:02:29.103Z", "scannedAt": "2025-07-21T02:02:29.103Z", "path": "domain/ui-ux-designer/thought/user-empathy.thought.md"}}, {"id": "ui-ux-designer", "source": "project", "protocol": "role", "name": "Ui Ux Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/ui-ux-designer/ui-ux-designer.role.md", "metadata": {"createdAt": "2025-07-21T02:02:29.103Z", "updatedAt": "2025-07-21T02:02:29.103Z", "scannedAt": "2025-07-21T02:02:29.103Z", "path": "domain/ui-ux-designer/ui-ux-designer.role.md"}}, {"id": "backend-developer", "source": "project", "protocol": "role", "name": "Backend Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/backend-developer/backend-developer.role.md", "metadata": {"createdAt": "2025-07-21T02:02:29.104Z", "updatedAt": "2025-07-21T02:02:29.104Z", "scannedAt": "2025-07-21T02:02:29.104Z", "path": "role/backend-developer/backend-developer.role.md"}}, {"id": "backend-development", "source": "project", "protocol": "execution", "name": "Backend Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/backend-developer/execution/backend-development.execution.md", "metadata": {"createdAt": "2025-07-21T02:02:29.104Z", "updatedAt": "2025-07-21T02:02:29.104Z", "scannedAt": "2025-07-21T02:02:29.104Z", "path": "role/backend-developer/execution/backend-development.execution.md"}}, {"id": "backend-thinking", "source": "project", "protocol": "thought", "name": "Backend Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/backend-developer/thought/backend-thinking.thought.md", "metadata": {"createdAt": "2025-07-21T02:02:29.105Z", "updatedAt": "2025-07-21T02:02:29.105Z", "scannedAt": "2025-07-21T02:02:29.105Z", "path": "role/backend-developer/thought/backend-thinking.thought.md"}}, {"id": "system-design", "source": "project", "protocol": "thought", "name": "System Design 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/backend-developer/thought/system-design.thought.md", "metadata": {"createdAt": "2025-07-21T02:02:29.105Z", "updatedAt": "2025-07-21T02:02:29.105Z", "scannedAt": "2025-07-21T02:02:29.105Z", "path": "role/backend-developer/thought/system-design.thought.md"}}, {"id": "frontend-development", "source": "project", "protocol": "execution", "name": "Frontend Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/frontend-developer/execution/frontend-development.execution.md", "metadata": {"createdAt": "2025-07-21T02:02:29.105Z", "updatedAt": "2025-07-21T02:02:29.105Z", "scannedAt": "2025-07-21T02:02:29.105Z", "path": "role/frontend-developer/execution/frontend-development.execution.md"}}, {"id": "frontend-developer", "source": "project", "protocol": "role", "name": "Frontend Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/frontend-developer/frontend-developer.role.md", "metadata": {"createdAt": "2025-07-21T02:02:29.106Z", "updatedAt": "2025-07-21T02:02:29.106Z", "scannedAt": "2025-07-21T02:02:29.106Z", "path": "role/frontend-developer/frontend-developer.role.md"}}, {"id": "frontend-thinking", "source": "project", "protocol": "thought", "name": "Frontend Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/frontend-developer/thought/frontend-thinking.thought.md", "metadata": {"createdAt": "2025-07-21T02:02:29.106Z", "updatedAt": "2025-07-21T02:02:29.106Z", "scannedAt": "2025-07-21T02:02:29.106Z", "path": "role/frontend-developer/thought/frontend-thinking.thought.md"}}, {"id": "problem-solving", "source": "project", "protocol": "thought", "name": "Problem Solving 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/frontend-developer/thought/problem-solving.thought.md", "metadata": {"createdAt": "2025-07-21T02:02:29.106Z", "updatedAt": "2025-07-21T02:02:29.106Z", "scannedAt": "2025-07-21T02:02:29.106Z", "path": "role/frontend-developer/thought/problem-solving.thought.md"}}], "stats": {"totalResources": 18, "byProtocol": {"execution": 5, "knowledge": 3, "thought": 7, "role": 3}, "bySource": {"project": 18}}}