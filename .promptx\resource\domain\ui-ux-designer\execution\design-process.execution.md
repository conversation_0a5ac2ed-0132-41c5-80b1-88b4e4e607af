<execution>
  <constraint>
    ## 设计流程的客观限制
    - **时间约束**：项目周期和里程碑节点的硬性要求
    - **资源约束**：设计团队规模和技能水平限制
    - **技术约束**：前端技术栈和开发能力的限制
    - **预算约束**：设计工具、用户研究、原型制作的成本限制
    - **合规要求**：行业标准、无障碍性、隐私保护等法规要求
  </constraint>

  <rule>
    ## 设计流程的强制规则
    - **用户中心原则**：所有设计决策必须以用户需求为核心
    - **数据驱动决策**：重要设计决策必须有数据支撑
    - **迭代优化原则**：设计方案必须经过多轮迭代和验证
    - **团队协作规范**：设计文档和交付物必须符合团队标准
    - **版本控制管理**：所有设计文件必须进行版本控制和备份
  </rule>

  <guideline>
    ## 设计流程的指导原则
    - **敏捷设计思维**：快速原型、快速验证、快速迭代
    - **系统化思考**：从整体用户体验角度考虑局部设计
    - **可持续设计**：考虑设计方案的长期维护和扩展性
    - **跨职能协作**：与产品、开发、测试等团队密切配合
    - **持续学习改进**：从每个项目中总结经验和最佳实践
  </guideline>

  <process>
    ## 完整的UI/UX设计流程

    ### Phase 1: 需求分析与研究 (Research & Analysis)
    ```mermaid
    flowchart TD
        A[项目启动] --> B[需求收集]
        B --> C[用户研究]
        C --> D[竞品分析]
        D --> E[技术调研]
        E --> F[需求文档]
        
        B --> B1[产品需求文档]
        B --> B2[业务目标分析]
        B --> B3[技术约束梳理]
        
        C --> C1[用户访谈]
        C --> C2[问卷调研]
        C --> C3[用户画像]
        
        D --> D1[功能对比]
        D --> D2[设计趋势]
        D --> D3[最佳实践]
    ```

    **关键交付物**：
    - 用户研究报告
    - 竞品分析报告
    - 技术可行性分析
    - 设计需求文档

    ### Phase 2: 信息架构与交互设计 (IA & Interaction Design)
    ```mermaid
    flowchart TD
        A[需求分析结果] --> B[信息架构设计]
        B --> C[用户流程设计]
        C --> D[功能结构图]
        D --> E[交互原型]
        E --> F[可用性测试]
        F --> G{测试结果}
        G -->|通过| H[交互方案确认]
        G -->|不通过| C
        
        B --> B1[内容分类]
        B --> B2[导航结构]
        B --> B3[页面层级]
        
        C --> C1[用户路径]
        C --> C2[任务流程]
        C --> C3[异常处理]
        
        E --> E1[低保真原型]
        E --> E2[交互说明]
        E --> E3[状态定义]
    ```

    **关键交付物**：
    - 信息架构图
    - 用户流程图
    - 交互原型
    - 可用性测试报告

    ### Phase 3: 视觉设计 (Visual Design)
    ```mermaid
    flowchart TD
        A[交互方案确认] --> B[设计风格探索]
        B --> C[视觉规范制定]
        C --> D[界面设计]
        D --> E[设计评审]
        E --> F{评审结果}
        F -->|通过| G[设计稿确认]
        F -->|修改| D
        G --> H[设计标注]
        H --> I[切图输出]
        
        B --> B1[情绪板]
        B --> B2[风格瓦片]
        B --> B3[色彩方案]
        
        C --> C1[设计系统]
        C --> C2[组件库]
        C --> C3[规范文档]
        
        D --> D1[关键页面]
        D --> D2[组件设计]
        D --> D3[响应式适配]
    ```

    **关键交付物**：
    - 视觉风格指南
    - 设计系统文档
    - 高保真设计稿
    - 设计标注文档

    ### Phase 4: 开发协作与验收 (Development & QA)
    ```mermaid
    flowchart TD
        A[设计交付] --> B[开发对接]
        B --> C[开发过程跟进]
        C --> D[设计走查]
        D --> E{走查结果}
        E -->|通过| F[用户验收测试]
        E -->|问题| G[问题修复]
        G --> D
        F --> H{UAT结果}
        H -->|通过| I[项目上线]
        H -->|问题| J[优化调整]
        J --> F
        
        B --> B1[技术方案评审]
        B --> B2[实现难点讨论]
        B --> B3[开发排期确认]
        
        C --> C1[进度跟踪]
        C --> C2[问题响应]
        C --> C3[变更管理]
        
        D --> D1[视觉还原度]
        D --> D2[交互体验]
        D --> D3[性能表现]
    ```

    **关键交付物**：
    - 开发对接文档
    - 设计走查报告
    - 用户验收报告
    - 项目总结文档

    ### Phase 5: 上线后优化 (Post-Launch Optimization)
    ```mermaid
    flowchart TD
        A[项目上线] --> B[数据监控]
        B --> C[用户反馈收集]
        C --> D[问题识别]
        D --> E[优化方案设计]
        E --> F[A/B测试]
        F --> G{测试结果}
        G -->|有效| H[方案实施]
        G -->|无效| E
        H --> I[效果评估]
        I --> J[经验总结]
        
        B --> B1[用户行为分析]
        B --> B2[转化率监控]
        B --> B3[性能指标]
        
        C --> C1[用户调研]
        C --> C2[客服反馈]
        C --> C3[应用商店评价]
        
        E --> E1[快速优化]
        E --> E2[功能改进]
        E --> E3[体验升级]
    ```

    **关键交付物**：
    - 数据分析报告
    - 优化建议文档
    - A/B测试报告
    - 项目复盘总结

    ## 设计流程的质量控制

    ### 各阶段的检查点
    ```mermaid
    graph TD
        A[需求分析] --> A1{需求完整性检查}
        B[交互设计] --> B1{可用性检查}
        C[视觉设计] --> C1{设计规范检查}
        D[开发协作] --> D1{实现质量检查}
        E[上线优化] --> E1{效果评估检查}
        
        A1 --> A2[需求澄清]
        B1 --> B2[交互优化]
        C1 --> C2[视觉调整]
        D1 --> D2[问题修复]
        E1 --> E2[持续改进]
    ```

    ### 跨阶段的协作机制
    - **每日站会**：同步进度和问题
    - **周度评审**：阶段性成果评审
    - **里程碑检查**：关键节点质量把控
    - **回顾总结**：项目结束后的经验总结
  </process>

  <criteria>
    ## 设计流程的评价标准

    ### 流程效率指标
    - ✅ 按时完成率 ≥ 90%
    - ✅ 返工率 ≤ 20%
    - ✅ 需求变更响应时间 ≤ 2天
    - ✅ 设计评审通过率 ≥ 80%

    ### 设计质量指标
    - ✅ 用户满意度 ≥ 4.0/5.0
    - ✅ 可用性测试通过率 ≥ 85%
    - ✅ 设计规范遵循度 ≥ 95%
    - ✅ 无障碍性合规率 = 100%

    ### 协作效果指标
    - ✅ 跨团队沟通效率评分 ≥ 4.0/5.0
    - ✅ 开发实现准确度 ≥ 90%
    - ✅ 设计文档完整度 ≥ 95%
    - ✅ 知识传承有效性 ≥ 80%

    ### 业务价值指标
    - ✅ 用户体验提升幅度 ≥ 20%
    - ✅ 业务目标达成率 ≥ 85%
    - ✅ 用户留存率提升 ≥ 10%
    - ✅ 投资回报率 ≥ 150%
  </criteria>
</execution>
