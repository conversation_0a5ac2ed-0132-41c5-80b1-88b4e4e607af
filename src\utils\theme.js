// 动态设置 Element Plus 的主题色系

// 处理主题样式
export function handleThemeStyle(theme) {
	// 将传入的颜色值 theme（如 #409EFF）设为 主色调。
	document.documentElement.style.setProperty('--el-color-primary', theme)
	for (let i = 1; i <= 9; i++) {
		// 根据主色调生成 9 种浅色变体
		document.documentElement.style.setProperty(`--el-color-primary-light-${i}`, `${getLightColor(theme, i / 10)}`)
	}
	for (let i = 1; i <= 9; i++) {
		// 根据主色调生成 9 种深色变体
		document.documentElement.style.setProperty(`--el-color-primary-dark-${i}`, `${getDarkColor(theme, i / 10)}`)
	}
}

// hex颜色转rgb颜色
// 示例 hexToRgb('#409EFF') // 返回 [64, 158, 255]
export function hexToRgb(str) {
	str = str.replace('#', '')
	let hexs = str.match(/../g)
	for (let i = 0; i < 3; i++) {
		hexs[i] = parseInt(hexs[i], 16)
	}
	return hexs
}

// rgb颜色转Hex颜色
// 示例 rgbToHex(64, 158, 255) // 返回 #408EFF
export function rgbToHex(r, g, b) {
	let hexs = [r.toString(16), g.toString(16), b.toString(16)]
	for (let i = 0; i < 3; i++) {
		if (hexs[i].length == 1) {
			hexs[i] = `0${hexs[i]}`
		}
	}
	return `#${hexs.join('')}`
}

// 变浅颜色值
// 根据主颜色和亮度等级（0~1），生成一个更亮的颜色
// 示例 getLightColor('#409EFF', 0.5) // 返回 "#a5d8ff"
export function getLightColor(color, level) {
	let rgb = hexToRgb(color)
	for (let i = 0; i < 3; i++) {
		rgb[i] = Math.floor((255 - rgb[i]) * level + rgb[i])
	}
	return rgbToHex(rgb[0], rgb[1], rgb[2])
}

// 变深颜色值
// 根据主颜色和亮度等级（0~1），生成一个更深的颜色
// 示例 getDarkColor('#409EFF', 0.5) // 返回 "#204d7f"
export function getDarkColor(color, level) {
	let rgb = hexToRgb(color)
	for (let i = 0; i < 3; i++) {
		rgb[i] = Math.floor(rgb[i] * (1 - level))
	}
	return rgbToHex(rgb[0], rgb[1], rgb[2])
}
