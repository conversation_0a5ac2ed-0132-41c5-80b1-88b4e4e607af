<!--
  应用主内容区组件 (App Main Content Component)

  功能说明：
  1. 系统主要内容展示区域容器
  2. 路由视图渲染和页面切换管理
  3. 页面缓存 (keep-alive) 功能实现
  4. 页面切换动画效果处理
  5. iframe 页面和普通页面的统一管理
  6. 版权信息组件集成
  7. 响应式布局高度计算和适配

  核心特性：
  - 基于 router-view 的动态组件渲染
  - keep-alive 缓存提升页面切换性能
  - fade-transform 过渡动画优化用户体验
  - 支持内嵌 iframe 页面显示
  - 自动适配不同布局模式的高度

  使用场景：
  - 系统核心业务页面展示
  - 多页面应用的内容容器
  - 页面状态保持和性能优化
  - 统一的页面布局和样式管理
-->
<template>
  <section class="app-main">
    <router-view v-slot="{ Component, route }">
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="tagsViewStore.cachedViews">
          <component v-if="!route.meta.link" :is="Component" :key="route.path"/>
        </keep-alive>
      </transition>
    </router-view>
    <iframe-toggle />
    <copyright />
  </section>
</template>

<script setup>
import copyright from "./Copyright/index"
import iframeToggle from "./IframeToggle/index"
import useTagsViewStore from '@/store/modules/tagsView'

const route = useRoute()
const tagsViewStore = useTagsViewStore()

onMounted(() => {
  addIframe()
})

watchEffect(() => {
  addIframe()
})

function addIframe() {
  if (route.meta.link) {
    useTagsViewStore().addIframeView(route)
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.app-main:has(.copyright) {
  padding-bottom: 36px;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 6px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}
</style>

