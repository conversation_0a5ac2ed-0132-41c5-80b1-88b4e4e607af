<!--
  版权信息组件 (Copyright Component)

  功能说明：
  1. 显示系统底部版权信息
  2. 支持动态控制显示/隐藏状态
  3. 版权内容可通过设置中心配置
  4. 固定定位在页面底部
  5. 响应式设计，适配不同屏幕尺寸

  使用场景：
  - 系统布局底部版权声明
  - 法律合规信息展示
  - 品牌标识显示
-->
<template>
  <footer v-if="visible" class="copyright">
    <span>{{ content }}</span>
  </footer>
</template>

<script setup>
import useSettingsStore from '@/store/modules/settings'

const settingsStore = useSettingsStore()

const visible = computed(() => settingsStore.footerVisible)
const content = computed(() => settingsStore.footerContent)
</script>

<style scoped>
.copyright {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 36px;
  padding: 10px 20px;
  text-align: right;
  background-color: #f8f8f8;
  color: #666;
  font-size: 14px;
  border-top: 1px solid #e7e7e7;
  z-index: 999;
}
</style>