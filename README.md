<p align="center">
	<img alt="logo" src="https://oscimg.oschina.net/oscnet/up-d3d0a9303e11d522a06cd263f3079027715.png">
</p>
<h1 align="center" style="margin: 30px 0 30px; font-weight: bold;">RuoYi v3.9.0</h1>
<h4 align="center">基于SpringBoot+Vue3前后端分离的Java快速开发框架</h4>
<p align="center">
	<a href="https://gitee.com/y_project/RuoYi-Vue/stargazers"><img src="https://gitee.com/y_project/RuoYi-Vue/badge/star.svg?theme=dark"></a>
	<a href="https://gitee.com/y_project/RuoYi-Vue"><img src="https://img.shields.io/badge/RuoYi-v3.9.0-brightgreen.svg"></a>
	<a href="https://gitee.com/y_project/RuoYi-Vue/blob/master/LICENSE"><img src="https://img.shields.io/github/license/mashape/apistatus.svg"></a>
</p>

## 平台简介

* 本仓库为前端技术栈 [Vue3](https://v3.cn.vuejs.org) + [Element Plus](https://element-plus.org/zh-CN) + [Vite](https://cn.vitejs.dev) 版本。
* 配套后端代码仓库地址[RuoYi-Vue](https://gitee.com/y_project/RuoYi-Vue) 或 [RuoYi-Vue-fast](https://gitcode.com/yangzongzhuan/RuoYi-Vue-fast) 版本。
* 前端技术栈（[Vue2](https://cn.vuejs.org) + [Element](https://github.com/ElemeFE/element) + [Vue CLI](https://cli.vuejs.org/zh)），请移步[RuoYi-Vue](https://gitee.com/y_project/RuoYi-Vue/tree/master/ruoyi-ui)。
* 阿里云折扣场：[点我进入](http://aly.ruoyi.vip)，腾讯云秒杀场：[点我进入](http://txy.ruoyi.vip)&nbsp;&nbsp;

## 前端运行

```bash
# 克隆项目
git clone https://github.com/yangzongzhuan/RuoYi-Vue3.git

# 进入项目目录
cd RuoYi-Vue3

# 安装依赖
yarn --registry=https://registry.npmmirror.com

# 启动服务
yarn dev

# 构建测试环境 yarn build:stage
# 构建生产环境 yarn build:prod
# 前端访问地址 http://localhost:80
```

## 内置功能

1. 用户管理：用户是系统操作者，该功能主要完成系统用户配置。
2. 部门管理：配置系统组织机构（公司、部门、小组），树结构展现支持数据权限。
3. 岗位管理：配置系统用户所属担任职务。
4. 菜单管理：配置系统菜单，操作权限，按钮权限标识等。
5. 角色管理：角色菜单权限分配、设置角色按机构进行数据范围权限划分。
6. 字典管理：对系统中经常使用的一些较为固定的数据进行维护。
7. 参数管理：对系统动态配置常用参数。
8. 通知公告：系统通知公告信息发布维护。
9. 操作日志：系统正常操作日志记录和查询；系统异常信息日志记录和查询。
10. 登录日志：系统登录日志记录查询包含登录异常。
11. 在线用户：当前系统中活跃用户状态监控。
12. 定时任务：在线（添加、修改、删除)任务调度包含执行结果日志。
13. 代码生成：前后端代码的生成（java、html、xml、sql）支持CRUD下载 。
14. 系统接口：根据业务代码自动生成相关的api接口文档。
15. 服务监控：监视当前系统CPU、内存、磁盘、堆栈等相关信息。
16. 缓存监控：对系统的缓存信息查询，命令统计等。
17. 在线构建器：拖动表单元素生成相应的HTML代码。
18. 连接池监视：监视当前系统数据库连接池状态，可进行分析SQL找出系统性能瓶颈。

## 在线体验

- admin/admin123
- 陆陆续续收到一些打赏，为了更好的体验已用于演示服务器升级。谢谢各位小伙伴。

演示地址：http://vue.ruoyi.vip
文档地址：http://doc.ruoyi.vip

## 演示图

<table>
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/cd1f90be5f2684f4560c9519c0f2a232ee8.jpg"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/1cbcf0e6f257c7d3a063c0e3f2ff989e4b3.jpg"/></td>
    </tr>
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-8074972883b5ba0622e13246738ebba237a.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-9f88719cdfca9af2e58b352a20e23d43b12.png"/></td>
    </tr>
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-39bf2584ec3a529b0d5a3b70d15c9b37646.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-936ec82d1f4872e1bc980927654b6007307.png"/></td>
    </tr>
	<tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-b2d62ceb95d2dd9b3fbe157bb70d26001e9.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-d67451d308b7a79ad6819723396f7c3d77a.png"/></td>
    </tr>	 
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/5e8c387724954459291aafd5eb52b456f53.jpg"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/644e78da53c2e92a95dfda4f76e6d117c4b.jpg"/></td>
    </tr>
	<tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-8370a0d02977eebf6dbf854c8450293c937.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-49003ed83f60f633e7153609a53a2b644f7.png"/></td>
    </tr>
	<tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-d4fe726319ece268d4746602c39cffc0621.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-c195234bbcd30be6927f037a6755e6ab69c.png"/></td>
    </tr>
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/b6115bc8c31de52951982e509930b20684a.jpg"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-5e4daac0bb59612c5038448acbcef235e3a.png"/></td>
    </tr>
</table>

## 📁 项目结构

```
RuoYi-Vue3/
├── 📁 bin/                          # 构建脚本目录
│   ├── 🔧 build.bat                 # Windows构建脚本
│   ├── 📦 package.bat               # Windows打包脚本
│   └── 🚀 run-web.bat               # Windows启动脚本
├── 📁 html/                         # 静态HTML文件
│   └── 🌐 ie.html                   # IE浏览器兼容页面
├── 📁 public/                       # 公共静态资源
│   └── 🎯 favicon.ico               # 网站图标
├── 📁 src/                          # 源代码目录
│   ├── 🎨 App.vue                   # 根组件
│   ├── 🚀 main.js                   # 应用入口文件
│   ├── 🔐 permission.js             # 路由权限控制
│   ├── ⚙️ settings.js               # 全局配置文件
│   ├── 📁 api/                      # API接口目录
│   │   ├── 🔑 login.js              # 登录相关接口
│   │   ├── 📋 menu.js               # 菜单相关接口
│   │   ├── 📁 merchant/             # 商户相关接口
│   │   ├── 📁 monitor/              # 监控相关接口
│   │   ├── 📁 system/               # 系统管理接口
│   │   └── 📁 tool/                 # 工具相关接口
│   ├── 📁 assets/                   # 静态资源目录
│   │   ├── 📁 401_images/           # 401错误页面图片
│   │   ├── 📁 404_images/           # 404错误页面图片
│   │   ├── 📁 icons/                # 图标资源
│   │   ├── 📁 images/               # 图片资源
│   │   ├── 📁 logo/                 # Logo图片
│   │   └── 📁 styles/               # 全局样式文件
│   ├── 📁 components/               # 公共组件目录
│   │   ├── 📁 Breadcrumb/           # 面包屑导航组件
│   │   ├── 📁 Crontab/              # 定时任务组件
│   │   ├── 📁 DictTag/              # 字典标签组件
│   │   ├── 📁 Editor/               # 富文本编辑器组件
│   │   ├── 📁 FileUpload/           # 文件上传组件
│   │   ├── 📁 Hamburger/            # 汉堡菜单组件
│   │   ├── 📁 HeaderSearch/         # 头部搜索组件
│   │   ├── 📁 IconSelect/           # 图标选择组件
│   │   ├── 📁 ImagePreview/         # 图片预览组件
│   │   ├── 📁 ImageUpload/          # 图片上传组件
│   │   ├── 📁 Pagination/           # 分页组件
│   │   ├── 📁 ParentView/           # 父级视图组件
│   │   ├── 📁 RightToolbar/         # 右侧工具栏组件
│   │   ├── 📁 RuoYi/                # RuoYi专用组件
│   │   ├── 📁 Screenfull/           # 全屏组件
│   │   ├── 📁 SizeSelect/           # 尺寸选择组件
│   │   ├── 📁 SvgIcon/              # SVG图标组件
│   │   ├── 📁 TopNav/               # 顶部导航组件
│   │   └── 📁 iFrame/               # 内嵌框架组件
│   ├── 📁 directive/                # 自定义指令目录
│   │   ├── 📄 index.js              # 指令入口文件
│   │   ├── 📁 common/               # 通用指令
│   │   └── 📁 permission/           # 权限指令
│   ├── 📁 layout/                   # 布局组件目录
│   │   ├── 🏗️ index.vue             # 主布局组件
│   │   └── 📁 components/           # 布局子组件
│   ├── 📁 plugins/                  # 插件目录
│   │   ├── 📄 index.js              # 插件入口文件
│   │   ├── 🔐 auth.js               # 认证插件
│   │   ├── 💾 cache.js              # 缓存插件
│   │   ├── 📥 download.js           # 下载插件
│   │   ├── 💬 modal.js              # 弹窗插件
│   │   └── 📑 tab.js                # 标签页插件
│   ├── 📁 router/                   # 路由配置目录
│   │   └── 🛣️ index.js              # 路由配置文件
│   ├── 📁 store/                    # 状态管理目录
│   │   ├── 📄 index.js              # Store入口文件
│   │   └── 📁 modules/              # Store模块
│   ├── 📁 utils/                    # 工具函数目录
│   │   ├── 🔐 auth.js               # 认证工具
│   │   ├── 📚 dict.js               # 字典工具
│   │   ├── 🏷️ dynamicTitle.js       # 动态标题工具
│   │   ├── ❌ errorCode.js          # 错误码工具
│   │   ├── 📄 index.js              # 工具函数入口
│   │   ├── 🔒 jsencrypt.js          # 加密工具
│   │   ├── 🔑 permission.js         # 权限工具
│   │   ├── 🌐 request.js            # 请求工具
│   │   ├── 🎨 ruoyi.js              # RuoYi工具
│   │   ├── 📜 scroll-to.js          # 滚动工具
│   │   ├── 🎨 theme.js              # 主题工具
│   │   ├── ✅ validate.js           # 验证工具
│   │   └── 📁 generator/            # 代码生成器工具
│   └── 📁 views/                    # 页面视图目录
│       ├── 🏠 index.vue             # 首页
│       ├── 🔑 login.vue             # 登录页
│       ├── 📝 register.vue          # 注册页
│       ├── 📁 error/                # 错误页面
│       ├── 📁 merchant/             # 商户管理页面
│       ├── 📁 monitor/              # 系统监控页面
│       ├── 📁 redirect/             # 重定向页面
│       ├── 📁 system/               # 系统管理页面
│       └── 📁 tool/                 # 系统工具页面
├── 📁 vite/                         # Vite配置目录
│   └── 📁 plugins/                  # Vite插件配置
├── ⚙️ vite.config.js                # Vite配置文件
├── 📦 package.json                  # 项目依赖配置
├── 🔒 yarn.lock                     # 依赖锁定文件
├── 🏠 index.html                    # 入口HTML文件
├── 📄 LICENSE                       # 开源协议
└── 📖 README.md                     # 项目说明文档
```

### 📋 目录说明

| 目录/文件                    | 说明                                 |
| ---------------------------- | ------------------------------------ |
| **📁 src/api/**        | 存放所有API接口定义，按业务模块分类  |
| **📁 src/assets/**     | 静态资源文件，包括图片、样式、图标等 |
| **📁 src/components/** | 可复用的Vue组件，提供通用功能        |
| **📁 src/directive/**  | 自定义Vue指令，扩展模板功能          |
| **📁 src/layout/**     | 页面布局组件，定义整体页面结构       |
| **📁 src/plugins/**    | 全局插件，提供通用功能和工具         |
| **📁 src/router/**     | 路由配置，定义页面导航和权限         |
| **📁 src/store/**      | Pinia状态管理，管理全局应用状态      |
| **📁 src/utils/**      | 工具函数库，提供通用的业务逻辑       |
| **📁 src/views/**      | 页面组件，对应具体的业务页面         |
| **📁 vite/**           | Vite构建工具配置和插件               |

### 🔧 核心文件说明

- **main.js**: 应用程序入口，初始化Vue应用和全局配置
- **App.vue**: 根组件，定义应用的整体结构
- **permission.js**: 路由守卫，控制页面访问权限
- **settings.js**: 全局配置文件，定义系统设置
- **vite.config.js**: Vite构建配置，定义开发和构建规则

## 依赖

### **核心依赖 (`dependencies`)**

1. **`@element-plus/icons-vue` (2.3.1)**
   * Element Plus 的 Vue 图标库，提供丰富的图标组件供项目使用。
2. **`@vueup/vue-quill` (1.2.0)**
   * Vue 3 版本的富文本编辑器 Quill 封装，用于创建可编辑的内容区域。
3. **`@vueuse/core` (10.11.0)**
   * VueUse 提供的实用函数集合，简化 Vue 项目的常见逻辑处理（如响应式、DOM 操作等）。
4. **`axios` (0.28.1)**
   * HTTP 请求库，用于发送网络请求，支持异步数据交互。
5. **[clipboard](javascript:void(0)) (2.0.11)**
   * 实现剪贴板操作，如复制、粘贴文本内容。
6. **`echarts` (5.5.1)**
   * 数据可视化图表库，支持柱状图、折线图、饼图等多种图形展示。
7. **`element-plus` (2.7.6)**
   * 基于 Vue 3 的 UI 组件库，提供企业级管理系统常用的界面组件（按钮、表格、对话框等）。
8. **`file-saver` (2.0.5)**
   * 文件保存工具，支持在浏览器中下载生成的文件（如 Excel、CSV 等）。
9. **`fuse.js` (6.6.2)**
   * 轻量级模糊搜索库，适用于本地搜索功能实现。
10. **`js-beautify` (1.14.11)**
    * 格式化代码（HTML、CSS、JS），常用于代码美化或格式转换。
11. **`js-cookie` (3.0.5)**
    * 简化 Cookie 的读写操作，便于管理浏览器端的用户状态信息。
12. **`jsencrypt` (3.3.2)**
    * RSA 加密库，用于前端加密敏感数据（如密码）后传输到后端解密。
13. **`nprogress` (0.2.0)**
    * 页面加载进度条库，常用于页面切换时显示加载动画。
14. **`pinia` (2.1.7)**
    * Vue 官方推荐的状态管理库，替代 Vuex，用于统一管理应用的数据状态。
15. **`splitpanes` (3.1.5)**
    * 可拆分面板布局组件，适用于需要多区域动态调整的应用场景。
16. **`vue` (3.4.31)**
    * Vue 3 核心框架，用于构建响应式的用户界面。
17. **`vue-cropper` (1.1.1)**
    * 图片裁剪组件，适用于上传头像等需要裁剪图片的场景。
18. **`vue-router` (4.4.0)**
    * Vue 官方路由管理库，用于实现单页应用中的导航和路径映射。
19. **`vuedraggable` (4.1.0)**
    * 支持拖拽排序的 Vue 组件，基于 Sortable.js 实现。

---

### **开发依赖 (`devDependencies`)**

1. **`@vitejs/plugin-vue` (5.0.5)**
   * Vite 插件，用于支持 Vue 项目的编译与打包。
2. **`sass` (1.77.5)**
   * CSS 预处理器 Sass，支持嵌套样式、变量定义等功能，提升 CSS 开发效率。
3. **`unplugin-auto-import` (0.17.6)**
   * 自动导入常用模块（如 `ref`, `reactive`, `onMounted` 等），减少手动 `import` 冗余代码。
4. **`unplugin-vue-setup-extend-plus` (1.0.1)**
   * 扩展 Vue 单文件组件的 [setup](javascript:void(0)) 语法，支持更多高级特性。
5. **`vite` (5.3.2)**
   * 前端构建工具，提供快速冷启动和即时热更新，适合现代 JS/TS/Vue 项目。
6. **`vite-plugin-compression` (0.5.1)**
   * 构建时压缩资源文件（如 Gzip），优化生产环境下的加载速度。
7. **`vite-plugin-svg-icons` (2.0.1)**
   * 自动将 SVG 图标转换为 Vue 组件，方便统一管理和使用图标资源。
