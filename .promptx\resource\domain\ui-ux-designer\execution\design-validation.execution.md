<execution>
  <constraint>
    ## 设计验证的客观限制
    - **时间压力**：项目周期对验证深度和广度的限制
    - **用户获取难度**：目标用户招募的时间和成本限制
    - **测试环境限制**：实验室环境与真实使用环境的差异
    - **样本代表性**：测试用户样本的代表性和规模限制
    - **主观性影响**：用户反馈的主观性和情境依赖性
  </constraint>

  <rule>
    ## 设计验证的强制规则
    - **多方法验证**：重要设计决策必须通过多种方法验证
    - **真实用户测试**：验证必须基于真实目标用户的反馈
    - **定量定性结合**：同时收集定量数据和定性洞察
    - **迭代验证原则**：每次重大设计变更都必须重新验证
    - **结果文档化**：所有验证结果必须详细记录和归档
  </rule>

  <guideline>
    ## 设计验证的指导原则
    - **早期频繁验证**：在设计过程中尽早和频繁地进行验证
    - **渐进式验证**：从低保真到高保真的渐进式验证策略
    - **场景化测试**：在接近真实使用场景的环境中进行测试
    - **多维度评估**：从可用性、满意度、效率等多个维度评估
    - **持续改进循环**：基于验证结果持续改进设计方案
  </guideline>

  <process>
    ## 设计验证的完整流程

    ### Phase 1: 验证计划制定 (Validation Planning)
    ```mermaid
    flowchart TD
        A[设计方案确定] --> B[确定验证目标]
        B --> C[选择验证方法]
        C --> D[制定测试计划]
        D --> E[准备测试材料]
        E --> F[招募测试用户]
        
        B --> B1[可用性验证]
        B --> B2[用户满意度验证]
        B --> B3[商业价值验证]
        B --> B4[技术可行性验证]
        
        C --> C1[可用性测试]
        C --> C2[A/B测试]
        C --> C3[专家评审]
        C --> C4[用户访谈]
        
        D --> D1[测试任务设计]
        D --> D2[成功标准定义]
        D --> D3[测试环境准备]
        D --> D4[时间安排]
    ```

    ### Phase 2: 可用性测试 (Usability Testing)
    ```mermaid
    flowchart TD
        A[测试准备] --> B[用户引导]
        B --> C[任务执行]
        C --> D[行为观察]
        D --> E[问题记录]
        E --> F[后续访谈]
        F --> G[数据整理]
        
        A --> A1[原型准备]
        A --> A2[测试环境]
        A --> A3[录制设备]
        A --> A4[观察表格]
        
        C --> C1[核心任务]
        C --> C2[次要任务]
        C --> C3[探索性任务]
        
        D --> D1[操作路径]
        D --> D2[停顿犹豫]
        D --> D3[错误操作]
        D --> D4[情感反应]
        
        E --> E1[功能性问题]
        E --> E2[可用性问题]
        E --> E3[用户体验问题]
    ```

    **可用性测试关键指标**：
    - **任务完成率**：用户成功完成任务的比例
    - **任务完成时间**：用户完成任务所需的平均时间
    - **错误率**：用户在完成任务过程中的错误次数
    - **满意度评分**：用户对使用体验的主观评价

    ### Phase 3: A/B测试验证 (A/B Testing)
    ```mermaid
    flowchart TD
        A[确定测试假设] --> B[设计实验方案]
        B --> C[创建测试版本]
        C --> D[流量分配]
        D --> E[数据收集]
        E --> F[统计分析]
        F --> G{结果显著性}
        G -->|显著| H[确定优胜方案]
        G -->|不显著| I[延长测试或重新设计]
        H --> J[全量发布]
        
        B --> B1[样本量计算]
        B --> B2[测试时长规划]
        B --> B3[成功指标定义]
        
        C --> C1[版本A：对照组]
        C --> C2[版本B：实验组]
        C --> C3[版本C：备选方案]
        
        E --> E1[点击率]
        E --> E2[转化率]
        E --> E3[停留时间]
        E --> E4[跳出率]
    ```

    **A/B测试最佳实践**：
    - **单一变量原则**：每次测试只改变一个关键变量
    - **足够样本量**：确保统计结果的可靠性
    - **测试时长控制**：避免外部因素对结果的影响
    - **分层抽样**：确保测试组的用户构成相似

    ### Phase 4: 专家评审 (Expert Review)
    ```mermaid
    graph TD
        A[准备评审材料] --> B[组建评审团队]
        B --> C[启发式评估]
        C --> D[认知走查]
        D --> E[可访问性审查]
        E --> F[问题汇总]
        F --> G[优先级排序]
        G --> H[改进建议]
        
        B --> B1[UX专家]
        B --> B2[领域专家]
        B --> B3[技术专家]
        B --> B4[产品专家]
        
        C --> C1[可见性原则]
        C --> C2[一致性原则]
        C --> C3[用户控制原则]
        C --> C4[错误预防原则]
        
        D --> D1[用户目标分析]
        D --> D2[操作序列评估]
        D --> D3[认知负荷评估]
    ```

    ### Phase 5: 用户反馈收集 (User Feedback Collection)
    ```mermaid
    flowchart TD
        A[确定反馈目标] --> B[选择收集方法]
        B --> C[设计反馈问题]
        C --> D[部署收集工具]
        D --> E[用户反馈收集]
        E --> F[反馈分析整理]
        F --> G[洞察提取]
        G --> H[改进建议]
        
        B --> B1[在线问卷]
        B --> B2[用户访谈]
        B --> B3[焦点小组]
        B --> B4[反馈组件]
        
        C --> C1[满意度问题]
        C --> C2[使用体验问题]
        C --> C3[功能需求问题]
        C --> C4[改进建议问题]
        
        E --> E1[定量反馈]
        E --> E2[定性反馈]
        E --> E3[行为数据]
    ```

    ## 验证结果分析与应用

    ### 数据分析框架
    ```mermaid
    graph TD
        A[原始数据] --> B[数据清洗]
        B --> C[描述性分析]
        C --> D[对比分析]
        D --> E[趋势分析]
        E --> F[相关性分析]
        F --> G[洞察提取]
        G --> H[设计建议]
        
        C --> C1[均值/中位数]
        C --> C2[分布情况]
        C --> C3[异常值识别]
        
        D --> D1[版本对比]
        D --> D2[用户群体对比]
        D --> D3[时间段对比]
        
        G --> G1[问题识别]
        G --> G2[机会发现]
        G --> G3[用户偏好]
    ```

    ### 问题优先级评估
    ```mermaid
    graph LR
        A[严重性评估] --> A1[影响用户数量]
        A --> A2[问题严重程度]
        A --> A3[业务影响程度]
        
        B[解决难度评估] --> B1[技术实现难度]
        B --> B2[资源投入需求]
        B --> B3[时间周期预估]
        
        C[优先级矩阵] --> C1[高严重性+低难度<br/>立即解决]
        C --> C2[高严重性+高难度<br/>重点规划]
        C --> C3[低严重性+低难度<br/>后续优化]
        C --> C4[低严重性+高难度<br/>暂不处理]
        
        style C1 fill:#4CAF50
        style C2 fill:#FF9800
        style C3 fill:#2196F3
        style C4 fill:#F44336
    ```

    ## 验证工具与技术

    ### 测试工具推荐
    - **原型工具**：Figma, Sketch, Adobe XD, Principle
    - **测试平台**：UserTesting, Maze, Lookback, Hotjar
    - **分析工具**：Google Analytics, Mixpanel, Amplitude
    - **A/B测试**：Optimizely, VWO, Google Optimize
    - **反馈收集**：Typeform, SurveyMonkey, Intercom

    ### 验证方法选择指南
    ```mermaid
    graph TD
        A[验证需求] --> B{验证阶段}
        B -->|早期概念| C[专家评审<br/>用户访谈]
        B -->|原型阶段| D[可用性测试<br/>认知走查]
        B -->|上线前| E[A/B测试<br/>Beta测试]
        B -->|上线后| F[数据分析<br/>用户反馈]
        
        C --> C1[快速验证]
        C --> C2[概念可行性]
        
        D --> D1[交互验证]
        D --> D2[可用性验证]
        
        E --> E1[效果验证]
        E --> E2[性能验证]
        
        F --> F1[持续优化]
        F --> F2[长期监控]
    ```
  </process>

  <criteria>
    ## 设计验证的评价标准

    ### 验证覆盖度
    - ✅ 核心功能验证覆盖率 = 100%
    - ✅ 用户路径验证覆盖率 ≥ 90%
    - ✅ 设备平台验证覆盖率 ≥ 95%
    - ✅ 用户群体验证覆盖率 ≥ 80%

    ### 验证质量指标
    - ✅ 测试用户代表性评分 ≥ 4.0/5.0
    - ✅ 测试环境真实性评分 ≥ 4.0/5.0
    - ✅ 数据收集完整性 ≥ 95%
    - ✅ 结果可信度评分 ≥ 4.0/5.0

    ### 验证效果指标
    - ✅ 问题发现率 ≥ 85%
    - ✅ 改进建议采纳率 ≥ 70%
    - ✅ 验证后用户满意度提升 ≥ 20%
    - ✅ 验证成本控制在预算内 ≤ 100%

    ### 验证效率指标
    - ✅ 验证周期控制 ≤ 计划时间
    - ✅ 问题响应速度 ≤ 2天
    - ✅ 验证报告完成时间 ≤ 3天
    - ✅ 团队验证技能提升 ≥ 15%
  </criteria>
</execution>
