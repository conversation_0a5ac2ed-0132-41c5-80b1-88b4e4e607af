<execution>
  <constraint>
    ## 后端开发的客观限制
    - **硬件资源**：服务器CPU、内存、存储、网络带宽限制
    - **数据库性能**：查询性能、并发连接数、事务处理能力
    - **网络延迟**：分布式系统间的网络通信延迟
    - **安全合规**：数据保护法规、行业安全标准要求
    - **成本预算**：基础设施成本、运维成本、人力成本
  </constraint>

  <rule>
    ## 后端开发的强制规则
    - **代码规范**：必须遵循团队统一的编码标准和最佳实践
    - **安全第一**：所有接口必须进行身份验证和权限控制
    - **数据完整性**：关键业务操作必须使用事务保证数据一致性
    - **日志记录**：所有关键操作必须记录详细的操作日志
    - **错误处理**：必须有完善的异常处理和错误恢复机制
  </rule>

  <guideline>
    ## 后端开发的指导原则
    - **性能优先**：在保证功能正确的前提下，优化系统性能
    - **安全意识**：时刻考虑安全威胁，实施纵深防御策略
    - **可维护性**：编写清晰、可读、易于维护的代码
    - **可扩展性**：设计时考虑系统的横向和纵向扩展能力
    - **监控可观测**：建立完善的监控、日志和告警体系
  </guideline>

  <process>
    ## 后端开发完整流程

    ### Phase 1: 需求分析与架构设计 (Requirements & Architecture)
    ```mermaid
    flowchart TD
        A[业务需求分析] --> B[非功能需求分析]
        B --> C[技术调研]
        C --> D[架构设计]
        D --> E[数据库设计]
        E --> F[接口设计]
        F --> G[技术选型]
        G --> H[开发计划]
        
        B --> B1[性能要求]
        B --> B2[安全要求]
        B --> B3[可用性要求]
        B --> B4[扩展性要求]
        
        D --> D1[系统架构]
        D --> D2[服务拆分]
        D --> D3[数据流设计]
        
        E --> E1[实体关系设计]
        E --> E2[数据库选型]
        E --> E3[索引设计]
    ```

    ### Phase 2: 核心开发 (Core Development)
    ```mermaid
    flowchart TD
        A[环境搭建] --> B[数据库初始化]
        B --> C[基础框架搭建]
        C --> D[核心业务开发]
        D --> E[API接口开发]
        E --> F[数据访问层]
        F --> G[业务逻辑层]
        G --> H[服务层开发]
        H --> I[单元测试]
        I --> J[集成测试]
        
        C --> C1[项目结构]
        C --> C2[配置管理]
        C --> C3[依赖管理]
        
        D --> D1[领域模型]
        D --> D2[业务规则]
        D --> D3[工作流程]
        
        E --> E1[RESTful API]
        E --> E2[参数验证]
        E --> E3[响应格式]
    ```

    ### Phase 3: 安全与性能 (Security & Performance)
    ```mermaid
    flowchart TD
        A[安全设计] --> B[身份认证]
        B --> C[权限控制]
        C --> D[数据加密]
        D --> E[安全测试]
        E --> F[性能基准测试]
        F --> G[性能优化]
        G --> H[缓存策略]
        H --> I[数据库优化]
        I --> J[负载测试]
        
        B --> B1[JWT Token]
        B --> B2[OAuth2.0]
        B --> B3[多因子认证]
        
        C --> C1[RBAC权限模型]
        C --> C2[API权限控制]
        C --> C3[数据权限控制]
        
        G --> G1[代码优化]
        G --> G2[算法优化]
        G --> G3[并发优化]
    ```

    ### Phase 4: 部署与监控 (Deployment & Monitoring)
    ```mermaid
    flowchart TD
        A[容器化] --> B[CI/CD配置]
        B --> C[环境配置]
        C --> D[数据库迁移]
        D --> E[服务部署]
        E --> F[负载均衡配置]
        F --> G[监控系统]
        G --> H[日志系统]
        H --> I[告警配置]
        I --> J[备份策略]
        
        A --> A1[Docker镜像]
        A --> A2[Kubernetes配置]
        A --> A3[服务编排]
        
        G --> G1[性能监控]
        G --> G2[业务监控]
        G --> G3[错误监控]
        
        H --> H1[结构化日志]
        H --> H2[日志聚合]
        H --> H3[日志分析]
    ```

    ### Phase 5: 运维与优化 (Operations & Optimization)
    ```mermaid
    flowchart TD
        A[生产监控] --> B[性能分析]
        B --> C[问题诊断]
        C --> D[故障处理]
        D --> E[系统优化]
        E --> F[容量规划]
        F --> G[版本升级]
        G --> H[安全加固]
        H --> I[灾难恢复]
        
        A --> A1[实时监控]
        A --> A2[健康检查]
        A --> A3[性能指标]
        
        C --> C1[日志分析]
        C --> C2[性能分析]
        C --> C3[错误追踪]
        
        E --> E1[数据库优化]
        E --> E2[缓存优化]
        E --> E3[代码优化]
    ```

    ## 技术栈选择指南

    ### 编程语言选择
    ```mermaid
    graph TD
        A[业务需求] --> B{项目特点}
        B -->|高性能| C[Go/Rust/C++]
        B -->|快速开发| D[Python/Ruby/PHP]
        B -->|企业级| E[Java/C#]
        B -->|现代化| F[Node.js/Kotlin]
        
        C --> C1[并发处理优秀]
        C --> C2[内存使用高效]
        
        D --> D1[开发效率高]
        D --> D2[生态丰富]
        
        E --> E1[稳定可靠]
        E --> E2[企业支持好]
    ```

    ### 数据库选择
    ```mermaid
    graph TD
        A[数据特点] --> B{数据类型}
        B -->|结构化数据| C[关系型数据库]
        B -->|半结构化| D[文档数据库]
        B -->|键值对| E[键值数据库]
        B -->|图数据| F[图数据库]
        
        C --> C1[MySQL/PostgreSQL]
        D --> D1[MongoDB/CouchDB]
        E --> E1[Redis/DynamoDB]
        F --> F1[Neo4j/ArangoDB]
    ```

    ### 架构模式选择
    ```mermaid
    graph TD
        A[项目规模] --> B{团队规模}
        B -->|小团队| C[单体架构]
        B -->|中等团队| D[模块化单体]
        B -->|大团队| E[微服务架构]
        B -->|超大团队| F[服务网格]
        
        C --> C1[简单直接]
        C --> C2[部署简单]
        
        E --> E1[独立部署]
        E --> E2[技术多样性]
    ```
  </process>

  <criteria>
    ## 后端开发的评价标准

    ### 功能质量指标
    - ✅ 功能完整性 = 100%
    - ✅ 业务逻辑正确性 = 100%
    - ✅ 数据一致性保证 = 100%
    - ✅ 接口规范遵循度 ≥ 95%

    ### 性能指标
    - ✅ API响应时间 ≤ 200ms (P95)
    - ✅ 数据库查询时间 ≤ 100ms (P95)
    - ✅ 系统吞吐量 ≥ 设计目标
    - ✅ 并发用户数 ≥ 设计目标

    ### 安全指标
    - ✅ 安全漏洞数量 = 0 (高危)
    - ✅ 身份认证覆盖率 = 100%
    - ✅ 权限控制覆盖率 = 100%
    - ✅ 数据加密覆盖率 = 100%

    ### 可靠性指标
    - ✅ 系统可用性 ≥ 99.9%
    - ✅ 故障恢复时间 ≤ 5分钟
    - ✅ 数据备份成功率 = 100%
    - ✅ 监控覆盖率 ≥ 95%

    ### 代码质量指标
    - ✅ 代码覆盖率 ≥ 80%
    - ✅ 代码规范遵循度 ≥ 95%
    - ✅ 代码复杂度控制在合理范围
    - ✅ 技术债务控制在可接受水平
  </criteria>
</execution>
