export { default as AppMain } from './AppMain'
export { default as Navbar } from './Navbar'
export { default as Settings } from './Settings'
export { default as TagsView } from './TagsView/index.vue'


/* 
在vue中导入 import { Settings } from '@/layout/components/index'
在js中导入  import { Settings } from '@/layout/components/index'

好处
1、统一管理：将多个组件统一从一个入口导出，便于维护
2、简化导入：使用者不需要知道组件的具体路径
3、可扩展性：可以轻松添加更多组件到这个导出文件中
*/