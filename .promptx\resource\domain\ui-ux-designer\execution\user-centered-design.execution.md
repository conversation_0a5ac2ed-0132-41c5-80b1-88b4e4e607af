<execution>
  <constraint>
    ## 用户中心设计的客观限制
    - **用户多样性**：不同用户群体的需求差异和冲突
    - **研究成本**：用户研究的时间和预算限制
    - **数据可得性**：用户行为数据的获取难度和隐私限制
    - **技术实现**：用户需求与技术可行性的平衡
    - **商业约束**：用户需求与商业目标的权衡
  </constraint>

  <rule>
    ## 用户中心设计的强制规则
    - **真实用户验证**：所有设计假设必须通过真实用户验证
    - **数据驱动决策**：用户相关决策必须基于可靠的用户数据
    - **可访问性合规**：设计必须满足无障碍访问标准
    - **隐私保护优先**：用户数据收集和使用必须合规
    - **持续用户反馈**：建立持续收集用户反馈的机制
  </rule>

  <guideline>
    ## 用户中心设计的指导原则
    - **深度共情理解**：真正理解用户的需求、痛点和期望
    - **场景化设计**：基于真实使用场景进行设计决策
    - **渐进式改进**：通过小步快跑的方式持续优化用户体验
    - **全生命周期关注**：关注用户从接触到离开的完整体验
    - **包容性设计**：确保设计对所有用户群体都友好
  </guideline>

  <process>
    ## 用户中心设计的实施流程

    ### Step 1: 用户研究与洞察 (User Research & Insights)
    ```mermaid
    flowchart TD
        A[确定研究目标] --> B[选择研究方法]
        B --> C[招募目标用户]
        C --> D[执行用户研究]
        D --> E[数据分析整理]
        E --> F[提取用户洞察]
        F --> G[形成用户画像]
        
        B --> B1[定性研究<br/>访谈/观察]
        B --> B2[定量研究<br/>问卷/数据]
        B --> B3[混合研究<br/>A/B测试]
        
        D --> D1[用户访谈]
        D --> D2[可用性测试]
        D --> D3[焦点小组]
        D --> D4[用户日记]
        
        F --> F1[需求洞察]
        F --> F2[行为模式]
        F --> F3[痛点识别]
        F --> F4[机会点发现]
    ```

    **研究方法选择指南**：
    - **探索性研究**：用户访谈、焦点小组、用户观察
    - **验证性研究**：可用性测试、A/B测试、问卷调研
    - **持续性研究**：用户行为分析、反馈收集、满意度调研

    ### Step 2: 用户需求分析与优先级 (Need Analysis & Prioritization)
    ```mermaid
    graph TD
        A[用户洞察] --> B[需求提取]
        B --> C[需求分类]
        C --> D[影响力评估]
        D --> E[实现难度评估]
        E --> F[需求优先级排序]
        F --> G[需求路线图]
        
        C --> C1[功能需求]
        C --> C2[体验需求]
        C --> C3[情感需求]
        C --> C4[性能需求]
        
        D --> D1[用户价值]
        D --> D2[业务价值]
        D --> D3[使用频率]
        D --> D4[用户覆盖面]
        
        E --> E1[技术复杂度]
        E --> E2[资源投入]
        E --> E3[时间周期]
        E --> E4[风险评估]
    ```

    **需求优先级矩阵**：
    ```mermaid
    graph LR
        A[高价值<br/>低成本] --> A1[立即执行]
        B[高价值<br/>高成本] --> B1[重点规划]
        C[低价值<br/>低成本] --> C1[资源允许时执行]
        D[低价值<br/>高成本] --> D1[暂不考虑]
        
        style A1 fill:#4CAF50
        style B1 fill:#FF9800
        style C1 fill:#2196F3
        style D1 fill:#F44336
    ```

    ### Step 3: 用户体验设计 (UX Design)
    ```mermaid
    flowchart TD
        A[需求优先级] --> B[用户旅程映射]
        B --> C[信息架构设计]
        C --> D[交互流程设计]
        D --> E[界面布局设计]
        E --> F[原型制作]
        F --> G[用户测试]
        G --> H{测试结果}
        H -->|通过| I[设计方案确认]
        H -->|不通过| J[问题分析]
        J --> K[设计优化]
        K --> F
        
        B --> B1[触点识别]
        B --> B2[情感曲线]
        B --> B3[痛点标注]
        B --> B4[机会点标注]
        
        C --> C1[内容结构]
        C --> C2[导航体系]
        C --> C3[搜索体系]
        
        D --> D1[任务流程]
        D --> D2[决策路径]
        D --> D3[异常处理]
    ```

    ### Step 4: 用户验证与迭代 (User Validation & Iteration)
    ```mermaid
    flowchart TD
        A[设计方案] --> B[制作测试原型]
        B --> C[设计测试计划]
        C --> D[招募测试用户]
        D --> E[执行可用性测试]
        E --> F[收集用户反馈]
        F --> G[分析测试结果]
        G --> H{是否需要优化}
        H -->|是| I[识别优化点]
        H -->|否| J[方案通过验证]
        I --> K[设计调整]
        K --> B
        
        C --> C1[测试目标]
        C --> C2[测试任务]
        C --> C3[成功标准]
        C --> C4[测试环境]
        
        E --> E1[任务完成情况]
        E --> E2[操作效率]
        E --> E3[错误率]
        E --> E4[满意度]
        
        G --> G1[定量分析]
        G --> G2[定性分析]
        G --> G3[问题归类]
        G --> G4[改进建议]
    ```

    ### Step 5: 持续优化与监控 (Continuous Optimization)
    ```mermaid
    flowchart TD
        A[产品上线] --> B[建立监控体系]
        B --> C[数据收集分析]
        C --> D[用户反馈收集]
        D --> E[问题识别]
        E --> F[优化方案设计]
        F --> G[小范围测试]
        G --> H{测试效果}
        H -->|正面| I[全面推广]
        H -->|负面| J[方案调整]
        J --> F
        I --> K[效果评估]
        K --> L[经验总结]
        L --> C
        
        B --> B1[用户行为监控]
        B --> B2[性能指标监控]
        B --> B3[业务指标监控]
        
        C --> C1[漏斗分析]
        C --> C2[路径分析]
        C --> C3[热力图分析]
        
        D --> D1[用户调研]
        D --> D2[客服反馈]
        D --> D3[评价反馈]
    ```

    ## 用户中心设计的工具与方法

    ### 用户研究工具箱
    ```mermaid
    mindmap
      root((用户研究工具))
        定性研究
          用户访谈
          焦点小组
          用户观察
          日记研究
        定量研究
          问卷调研
          数据分析
          A/B测试
          眼动测试
        混合研究
          可用性测试
          卡片分类
          树状测试
          第一印象测试
    ```

    ### 设计验证方法
    - **原型测试**：低保真到高保真的渐进式验证
    - **A/B测试**：不同设计方案的对比验证
    - **专家评审**：基于可用性原则的专业评估
    - **认知走查**：模拟用户认知过程的评估方法
  </process>

  <criteria>
    ## 用户中心设计的评价标准

    ### 用户满意度指标
    - ✅ 整体满意度评分 ≥ 4.0/5.0
    - ✅ 推荐意愿 (NPS) ≥ 50
    - ✅ 用户留存率 ≥ 70%
    - ✅ 用户活跃度提升 ≥ 15%

    ### 可用性指标
    - ✅ 任务完成率 ≥ 90%
    - ✅ 任务完成时间减少 ≥ 20%
    - ✅ 错误率 ≤ 5%
    - ✅ 学习曲线斜率 ≥ 80%

    ### 可访问性指标
    - ✅ WCAG 2.1 AA级合规率 = 100%
    - ✅ 多设备适配完成度 = 100%
    - ✅ 多语言支持准确度 ≥ 95%
    - ✅ 无障碍功能覆盖率 = 100%

    ### 业务影响指标
    - ✅ 转化率提升 ≥ 10%
    - ✅ 用户支持请求减少 ≥ 30%
    - ✅ 用户生命周期价值提升 ≥ 20%
    - ✅ 品牌认知度提升 ≥ 15%
  </criteria>
</execution>
