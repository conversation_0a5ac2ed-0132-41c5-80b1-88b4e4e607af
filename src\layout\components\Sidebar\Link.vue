<!--
  智能链接组件 (Smart Link Component)

  功能说明：
  1. 智能判断链接类型 (内部路由 vs 外部链接)
  2. 动态渲染 router-link 或 a 标签
  3. 外部链接自动添加安全属性 (target="_blank", rel="noopener")
  4. 内部路由使用 Vue Router 导航
  5. 统一的链接行为和样式管理

  使用场景：
  - 菜单项链接统一处理
  - 内外部链接自动识别
  - 安全的外部链接跳转
  - 路由导航封装
-->
<template>
  <component :is="type" v-bind="linkProps()">
    <slot />
  </component>
</template>

<script setup>
import { isExternal } from '@/utils/validate'

const props = defineProps({
  to: {
    type: [String, Object],
    required: true
  }
})

const isExt = computed(() => {
  return isExternal(props.to)
})

const type = computed(() => {
  if (isExt.value) {
    return 'a'
  }
  return 'router-link'
})

function linkProps() {
  if (isExt.value) {
    return {
      href: props.to,
      target: '_blank',
      rel: 'noopener'
    }
  }
  return {
    to: props.to
  }
}
</script>
